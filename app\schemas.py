from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    age: int
    gender: str
    height: float
    weight: float
    medical_conditions: Optional[str] = None
    medications: Optional[str] = None

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int

    class Config:
        orm_mode = True

# Blood Pressure schemas
class BloodPressureBase(BaseModel):
    systolic: int
    diastolic: int
    pulse: int
    notes: Optional[str] = None
    device_id: Optional[str] = None
    interpretation: Optional[str] = None

class BloodPressureCreate(BloodPressureBase):
    pass

class BloodPressure(BloodPressureBase):
    id: int
    user_id: int
    reading_time: datetime

    class Config:
        orm_mode = True

# Device Upload schema for future OCR implementation
class DeviceImageUpload(BaseModel):
    image_data: str  # Base64 encoded image