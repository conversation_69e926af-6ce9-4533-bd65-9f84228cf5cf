"""
Azure AI Foundry Agent for personalized blood pressure recommendations.
"""
import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from azure.ai.projects import AIProjectClient
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv

from .database_tools import DatabaseTools

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Azure AI Foundry Configuration
AZURE_AI_PROJECT_ENDPOINT = os.getenv("AZURE_AI_PROJECT_ENDPOINT")
AZURE_AI_MODEL_DEPLOYMENT = os.getenv("AZURE_AI_MODEL_DEPLOYMENT", "gpt-4o")


class RecommendationAgent:
    """Azure AI Foundry Agent for generating personalized health recommendations."""
    
    def __init__(self):
        """Initialize the recommendation agent."""
        self.project_client = None
        self.db_tools = DatabaseTools()
        self.agent_id = None
        self.thread_id = None
        
        # Check for required environment variables
        if not AZURE_AI_PROJECT_ENDPOINT:
            logger.error("AZURE_AI_PROJECT_ENDPOINT environment variable is required")
            raise ValueError("Missing Azure AI Project endpoint configuration")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
    
    async def initialize(self):
        """Initialize the Azure AI Project client and create agent."""
        try:
            # Create an AIProjectClient instance
            self.project_client = AIProjectClient(
                endpoint=AZURE_AI_PROJECT_ENDPOINT,
                credential=DefaultAzureCredential(),
            )
            
            # Define database tools for the agent
            tool_definitions = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_user_profile",
                        "description": "Get comprehensive user profile information including demographics, BMI, medical conditions, and medications",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "user_id": {
                                    "type": "integer",
                                    "description": "The ID of the user to get profile for"
                                }
                            },
                            "required": ["user_id"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "get_recent_bp_readings",
                        "description": "Get recent blood pressure readings and statistics for a user",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "user_id": {
                                    "type": "integer",
                                    "description": "The ID of the user"
                                },
                                "days": {
                                    "type": "integer",
                                    "description": "Number of days to look back (default: 30)",
                                    "default": 30
                                }
                            },
                            "required": ["user_id"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "get_bp_trends",
                        "description": "Analyze blood pressure trends over time to identify patterns",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "user_id": {
                                    "type": "integer",
                                    "description": "The ID of the user"
                                },
                                "days": {
                                    "type": "integer",
                                    "description": "Number of days to analyze (default: 90)",
                                    "default": 90
                                }
                            },
                            "required": ["user_id"]
                        }
                    }
                }
            ]
            
            # Create an agent with database tools
            with self.project_client:
                self.agent = self.project_client.agents.create_agent(
                    model=AZURE_AI_MODEL_DEPLOYMENT,
                    name="CardioMed Health Advisor",
                    instructions="""You are CardioMed Health Advisor, a specialized AI assistant for cardiovascular health management. 

Your role is to provide personalized, evidence-based recommendations for blood pressure management based on:
- User's demographic information (age, gender, BMI)
- Medical history and current medications
- Recent blood pressure readings and trends
- Established medical guidelines (AHA/ESC)

IMPORTANT GUIDELINES:
1. Always start by gathering user profile and recent BP data using the available tools
2. Provide actionable, specific recommendations tailored to the individual
3. Consider the user's medical conditions and medications when making suggestions
4. Include lifestyle modifications, monitoring recommendations, and when to seek medical care
5. Use a warm, encouraging, but professional tone
6. Always include appropriate medical disclaimers
7. Focus on evidence-based interventions
8. Provide morning check-up style recommendations that are practical for daily implementation

RECOMMENDATION CATEGORIES:
- Lifestyle modifications (diet, exercise, stress management)
- Monitoring schedule and targets
- Medication adherence reminders (if applicable)
- Warning signs to watch for
- When to contact healthcare provider
- Daily/weekly health tips

Remember: You are providing health education and support, not medical diagnosis or treatment. Always encourage users to work with their healthcare providers.""",
                    tools=tool_definitions,
                )
                
                self.agent_id = self.agent.id
                logger.info(f"Created CardioMed Health Advisor agent, ID: {self.agent_id}")
                
        except Exception as e:
            logger.error(f"Failed to initialize recommendation agent: {e}")
            raise
    
    async def generate_morning_recommendations(self, user_id: int) -> Dict[str, Any]:
        """
        Generate personalized morning recommendations for a user.
        
        Args:
            user_id: The ID of the user to generate recommendations for
            
        Returns:
            Dictionary containing the recommendations and metadata
        """
        if not self.project_client or not self.agent_id:
            raise RuntimeError("Agent not initialized. Call initialize() first.")
        
        try:
            with self.project_client:
                # Create a new thread for this conversation
                thread = self.project_client.agents.threads.create()
                self.thread_id = thread.id
                logger.info(f"Created thread for user {user_id}, ID: {self.thread_id}")
                
                # Create the morning recommendation request message
                current_time = datetime.now().strftime("%A, %B %d, %Y at %I:%M %p")
                message_content = f"""Good morning! It's {current_time}. 

Please provide personalized morning health recommendations for user ID {user_id}. 

I need you to:
1. First, get their user profile and recent blood pressure data
2. Analyze their current health status and trends
3. Provide a comprehensive morning health briefing that includes:
   - Current health status summary
   - Specific recommendations for today
   - Blood pressure monitoring guidance
   - Lifestyle tips relevant to their condition
   - Any important reminders or alerts
   - Encouragement and motivation

Make the recommendations practical, actionable, and personalized to their specific situation."""
                
                # Add the message to the thread
                message = self.project_client.agents.messages.create(
                    thread_id=self.thread_id,
                    role="user",
                    content=message_content,
                )
                logger.info(f"Created message, ID: {message['id']}")
                
                # Create a run for the agent to process the message
                run = self.project_client.agents.runs.create(
                    thread_id=self.thread_id, 
                    agent_id=self.agent_id
                )
                logger.info(f"Created run, ID: {run.id}")
                
                # Poll the run status and handle tool calls
                while run.status in ["queued", "in_progress", "requires_action"]:
                    await asyncio.sleep(1)
                    run = self.project_client.agents.runs.get(
                        thread_id=self.thread_id, 
                        run_id=run.id
                    )
                    
                    if run.status == "requires_action":
                        tool_calls = run.required_action.submit_tool_outputs.tool_calls
                        tool_outputs = []
                        
                        for tool_call in tool_calls:
                            function_name = tool_call.function.name
                            function_args = json.loads(tool_call.function.arguments)
                            
                            logger.info(f"Executing tool: {function_name} with args: {function_args}")
                            
                            # Execute the appropriate database tool
                            if function_name == "get_user_profile":
                                result = await self.db_tools.get_user_profile(function_args["user_id"])
                            elif function_name == "get_recent_bp_readings":
                                result = await self.db_tools.get_recent_bp_readings(
                                    function_args["user_id"], 
                                    function_args.get("days", 30)
                                )
                            elif function_name == "get_bp_trends":
                                result = await self.db_tools.get_bp_trends(
                                    function_args["user_id"], 
                                    function_args.get("days", 90)
                                )
                            else:
                                result = {"error": f"Unknown function: {function_name}"}
                            
                            tool_outputs.append({
                                "tool_call_id": tool_call.id, 
                                "output": json.dumps(result)
                            })
                        
                        # Submit tool outputs
                        run = self.project_client.agents.runs.submit_tool_outputs(
                            thread_id=self.thread_id,
                            run_id=run.id,
                            tool_outputs=tool_outputs
                        )
                
                logger.info(f"Run finished with status: {run.status}")
                
                # Get the final response
                messages = self.project_client.agents.messages.list(thread_id=self.thread_id)
                
                # Find the assistant's response
                assistant_response = None
                for message in messages:
                    if hasattr(message, 'role') and message.role == "assistant":
                        if isinstance(message.content, list) and len(message.content) > 0:
                            assistant_response = message.content[0]['text']['value']
                        else:
                            assistant_response = str(message.content)
                        break
                
                if not assistant_response:
                    raise RuntimeError("No response received from agent")
                
                return {
                    "user_id": user_id,
                    "timestamp": datetime.now().isoformat(),
                    "recommendations": assistant_response,
                    "agent_id": self.agent_id,
                    "thread_id": self.thread_id,
                    "run_status": run.status
                }
                
        except Exception as e:
            logger.error(f"Error generating recommendations for user {user_id}: {e}")
            raise
    
    async def cleanup(self):
        """Clean up resources."""
        try:
            # Close database session
            self.db_tools.close_session()
            
            # Optionally delete the agent (uncomment if you want to clean up)
            # if self.project_client and self.agent_id:
            #     with self.project_client:
            #         self.project_client.agents.delete_agent(self.agent_id)
            #         logger.info(f"Deleted agent {self.agent_id}")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Convenience function for one-off recommendations
async def get_morning_recommendations(user_id: int) -> Dict[str, Any]:
    """
    Convenience function to get morning recommendations for a user.
    
    Args:
        user_id: The ID of the user
        
    Returns:
        Dictionary containing the recommendations
    """
    async with RecommendationAgent() as agent:
        return await agent.generate_morning_recommendations(user_id)
